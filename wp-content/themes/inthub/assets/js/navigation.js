/**
 * Navigation JavaScript for IntHub theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initNavigation();
    });

    /**
     * Initialize navigation functionality
     */
    function initNavigation() {
        initMobileMenu();
        initStickyHeader();
        initActiveMenuItems();
        initDropdownMenus();

        // Initialize mega menu if available
        if (typeof window.IntHubMegaMenu !== 'undefined') {
            window.IntHubMegaMenu.init();
        }
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        var $mobileToggle = $('.mobile-menu-toggle');
        var $mobileNav = $('#mobile-navigation');
        var $body = $('body');
        var isOpen = false;

        // Toggle mobile menu
        $mobileToggle.on('click', function(e) {
            e.preventDefault();
            toggleMobileMenu();
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (isOpen && !$(e.target).closest('.site-header').length) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && isOpen) { // Escape key
                closeMobileMenu();
            }
        });

        // Close mobile menu when window is resized to desktop
        $(window).on('resize', function() {
            if ($(window).width() >= 768 && isOpen) {
                closeMobileMenu();
            }
        });

        // Handle mobile menu links
        $mobileNav.find('a').on('click', function() {
            // Close menu when clicking on anchor links
            if ($(this).attr('href').indexOf('#') !== -1) {
                setTimeout(closeMobileMenu, 300);
            }
        });

        function toggleMobileMenu() {
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }

        function openMobileMenu() {
            isOpen = true;
            $mobileNav.removeClass('hidden').addClass('block');
            $mobileToggle.attr('aria-expanded', 'true');
            $body.addClass('mobile-menu-open');
            
            // Change hamburger to X
            updateMobileToggleIcon(true);
            
            // Animate menu items
            $mobileNav.find('li').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateY(-10px)'
                }).delay(index * 50).animate({
                    'opacity': '1'
                }, 200, function() {
                    $(this).css('transform', 'translateY(0)');
                });
            });
        }

        function closeMobileMenu() {
            isOpen = false;
            $mobileNav.removeClass('block').addClass('hidden');
            $mobileToggle.attr('aria-expanded', 'false');
            $body.removeClass('mobile-menu-open');
            
            // Change X back to hamburger
            updateMobileToggleIcon(false);
        }

        function updateMobileToggleIcon(isOpen) {
            var $icon = $mobileToggle.find('svg');
            if (isOpen) {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>');
            } else {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>');
            }
        }
    }

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var $header = $('.site-header');
        var $window = $(window);
        var headerHeight = $header.outerHeight();
        var scrollThreshold = 100;
        var isSticky = false;

        function updateStickyHeader() {
            var scrollTop = $window.scrollTop();
            
            if (scrollTop > scrollThreshold && !isSticky) {
                isSticky = true;
                $header.addClass('header-sticky');
                $('body').css('padding-top', headerHeight + 'px');
            } else if (scrollTop <= scrollThreshold && isSticky) {
                isSticky = false;
                $header.removeClass('header-sticky');
                $('body').css('padding-top', '0');
            }
        }

        // Throttle scroll events for better performance
        var throttledUpdate = throttle(updateStickyHeader, 10);
        $window.on('scroll', throttledUpdate);

        // Update on resize
        $window.on('resize', function() {
            headerHeight = $header.outerHeight();
            if (isSticky) {
                $('body').css('padding-top', headerHeight + 'px');
            }
        });

        // Add CSS for sticky header
        if (!$('#sticky-header-styles').length) {
            $('<style id="sticky-header-styles">')
                .text(`
                    .site-header {
                        transition: all 0.3s ease;
                    }
                    .site-header.header-sticky {
                        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                        backdrop-filter: blur(10px);
                        background: rgba(255, 255, 255, 0.95);
                    }
                    body.mobile-menu-open {
                        overflow: hidden;
                    }
                `)
                .appendTo('head');
        }
    }

    /**
     * Initialize active menu items
     */
    function initActiveMenuItems() {
        var currentUrl = window.location.href;
        var currentPath = window.location.pathname;

        // Mark current page menu item as active
        $('.main-navigation a, #mobile-navigation a').each(function() {
            var $link = $(this);
            var href = $link.attr('href');

            if (href === currentUrl || href === currentPath || 
                (href !== '#' && currentUrl.indexOf(href) !== -1)) {
                $link.addClass('current-menu-item');
                $link.closest('li').addClass('current-menu-item');
            }
        });

        // Handle section highlighting for single page navigation
        if ($('body').hasClass('home-page')) {
            initSectionHighlighting();
        }
    }

    /**
     * Initialize section highlighting for single page navigation
     */
    function initSectionHighlighting() {
        var $sections = $('section[id]');
        var $navLinks = $('.main-navigation a[href*="#"], #mobile-navigation a[href*="#"]');
        var headerHeight = $('.site-header').outerHeight() || 0;

        function updateActiveSection() {
            var scrollTop = $(window).scrollTop() + headerHeight + 50;
            var currentSection = '';

            $sections.each(function() {
                var $section = $(this);
                var sectionTop = $section.offset().top;
                var sectionBottom = sectionTop + $section.outerHeight();

                if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                    currentSection = $section.attr('id');
                }
            });

            // Update active nav links
            $navLinks.removeClass('active-section');
            if (currentSection) {
                $navLinks.filter('[href*="#' + currentSection + '"]').addClass('active-section');
            }
        }

        // Throttle scroll events
        var throttledUpdate = throttle(updateActiveSection, 100);
        $(window).on('scroll', throttledUpdate);

        // Initial update
        updateActiveSection();
    }

    /**
     * Initialize dropdown menus (if any)
     */
    function initDropdownMenus() {
        var $dropdownToggles = $('.menu-item-has-children > a');

        $dropdownToggles.each(function() {
            var $toggle = $(this);
            var $submenu = $toggle.siblings('.sub-menu');

            if ($submenu.length) {
                // Add dropdown arrow
                $toggle.append('<span class="dropdown-arrow ml-1"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></span>');

                // Desktop hover behavior
                $toggle.parent().on('mouseenter', function() {
                    if ($(window).width() >= 768) {
                        $submenu.addClass('show');
                    }
                }).on('mouseleave', function() {
                    if ($(window).width() >= 768) {
                        $submenu.removeClass('show');
                    }
                });

                // Mobile click behavior
                $toggle.on('click', function(e) {
                    if ($(window).width() < 768) {
                        e.preventDefault();
                        $submenu.toggleClass('show');
                        $(this).find('.dropdown-arrow').toggleClass('rotate-180');
                    }
                });
            }
        });
    }

    /**
     * Utility function to throttle events
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() {
                    inThrottle = false;
                }, limit);
            }
        };
    }

    // Expose navigation functions globally if needed
    window.IntHubNavigation = {
        closeMobileMenu: function() {
            $('.mobile-menu-toggle').trigger('click');
        }
    };

})(jQuery);
